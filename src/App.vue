<script setup>
import { ref, onMounted } from 'vue'
import pb from './pocketbase'

// 认证相关数据
const isLoggedIn = ref(false)
const currentUser = ref(null)
const showLogin = ref(true) // true: 登录, false: 注册

// 登录注册表单数据
const email = ref('')
const password = ref('')
const confirmPassword = ref('')

// 文章相关数据
const posts = ref([])
const newTitle = ref('')
const newContent = ref('')
const loading = ref(false)
const error = ref('')

// 检查登录状态
function checkAuth() {
  isLoggedIn.value = pb.authStore.isValid
  currentUser.value = pb.authStore.record
}

// 登录
async function login() {
  if (!email.value.trim() || !password.value.trim()) {
    error.value = '请填写邮箱和密码'
    return
  }

  try {
    loading.value = true
    error.value = ''

    const authData = await pb.collection('users').authWithPassword(email.value, password.value)

    isLoggedIn.value = true
    currentUser.value = authData.record

    // 清空表单
    email.value = ''
    password.value = ''

    // 登录成功后获取文章
    await getPosts()
  } catch (err) {
    error.value = '登录失败: ' + err.message
    console.error('登录失败:', err)
  } finally {
    loading.value = false
  }
}

// 注册
async function register() {
  if (!email.value.trim() || !password.value.trim() || !confirmPassword.value.trim()) {
    error.value = '请填写所有字段'
    return
  }

  if (password.value !== confirmPassword.value) {
    error.value = '两次输入的密码不一致'
    return
  }

  if (password.value.length < 6) {
    error.value = '密码长度至少6位'
    return
  }

  try {
    loading.value = true
    error.value = ''

    // 创建用户
    const userData = {
      email: email.value,
      password: password.value,
      passwordConfirm: confirmPassword.value,
    }

    await pb.collection('users').create(userData)

    // 注册成功后自动登录
    await login()
  } catch (err) {
    error.value = '注册失败: ' + err.message
    console.error('注册失败:', err)
    loading.value = false
  }
}

// 登出
function logout() {
  pb.authStore.clear()
  isLoggedIn.value = false
  currentUser.value = null
  posts.value = []

  // 清空表单
  email.value = ''
  password.value = ''
  confirmPassword.value = ''
  newTitle.value = ''
  newContent.value = ''
}

// 获取所有文章
async function getPosts() {
  try {
    loading.value = true
    error.value = ''
    const records = await pb.collection('posts').getFullList({
      sort: '-created',
    })
    posts.value = records
  } catch (err) {
    error.value = '获取数据失败: ' + err.message
    console.error('获取文章失败:', err)
  } finally {
    loading.value = false
  }
}

// 添加新文章
async function addPost() {
  if (!newTitle.value.trim() || !newContent.value.trim()) return

  try {
    loading.value = true
    error.value = ''
    const data = {
      title: newTitle.value,
      content: newContent.value,
    }

    const record = await pb.collection('posts').create(data)
    posts.value.unshift(record) // 添加到开头
    newTitle.value = ''
    newContent.value = ''
  } catch (err) {
    error.value = '添加失败: ' + err.message
    console.error('添加文章失败:', err)
  } finally {
    loading.value = false
  }
}

// 删除文章
async function deletePost(postId) {
  try {
    await pb.collection('posts').delete(postId)
    posts.value = posts.value.filter((p) => p.id !== postId)
  } catch (err) {
    error.value = '删除失败: ' + err.message
    console.error('删除文章失败:', err)
  }
}

// 组件挂载时检查登录状态
onMounted(() => {
  checkAuth()
  if (isLoggedIn.value) {
    getPosts()
  }
})
</script>

<template>
  <div class="app">
    <h1>PocketBase 文章管理</h1>

    <!-- 错误提示 -->
    <div v-if="error" class="error">
      {{ error }}
    </div>

    <!-- 未登录状态 - 显示登录注册表单 -->
    <div v-if="!isLoggedIn" class="auth-container">
      <div class="auth-tabs">
        <button @click="showLogin = true" :class="{ active: showLogin }" class="tab-btn">
          登录
        </button>
        <button @click="showLogin = false" :class="{ active: !showLogin }" class="tab-btn">
          注册
        </button>
      </div>

      <!-- 登录表单 -->
      <div v-if="showLogin" class="auth-form">
        <h2>用户登录</h2>
        <input
          v-model="email"
          type="email"
          placeholder="邮箱地址"
          :disabled="loading"
          @keyup.enter="login"
        />
        <input
          v-model="password"
          type="password"
          placeholder="密码"
          :disabled="loading"
          @keyup.enter="login"
        />
        <button @click="login" :disabled="loading || !email.trim() || !password.trim()">
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </div>

      <!-- 注册表单 -->
      <div v-else class="auth-form">
        <h2>用户注册</h2>
        <input v-model="email" type="email" placeholder="邮箱地址" :disabled="loading" />
        <input
          v-model="password"
          type="password"
          placeholder="密码（至少6位）"
          :disabled="loading"
        />
        <input
          v-model="confirmPassword"
          type="password"
          placeholder="确认密码"
          :disabled="loading"
          @keyup.enter="register"
        />
        <button
          @click="register"
          :disabled="loading || !email.trim() || !password.trim() || !confirmPassword.trim()"
        >
          {{ loading ? '注册中...' : '注册' }}
        </button>
      </div>
    </div>

    <!-- 已登录状态 - 显示文章管理 -->
    <div v-else class="main-content">
      <!-- 用户信息和登出 -->
      <div class="user-header">
        <span class="welcome">欢迎，{{ currentUser?.email }}</span>
        <button @click="logout" class="logout-btn">登出</button>
      </div>

      <!-- 添加新文章 -->
      <div class="add-post">
        <input
          v-model="newTitle"
          placeholder="文章标题..."
          :disabled="loading"
          class="title-input"
        />
        <textarea
          v-model="newContent"
          placeholder="文章内容..."
          :disabled="loading"
          class="content-input"
          rows="4"
        ></textarea>
        <button @click="addPost" :disabled="loading || !newTitle.trim() || !newContent.trim()">
          {{ loading ? '发布中...' : '发布文章' }}
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && posts.length === 0" class="loading">加载中...</div>

      <!-- 文章列表 -->
      <div v-else-if="posts.length > 0" class="posts">
        <div v-for="post in posts" :key="post.id" class="post-item">
          <div class="post-header">
            <h3 class="post-title">{{ post.title }}</h3>
            <div class="post-meta">
              <span class="post-date">{{ new Date(post.created).toLocaleString() }}</span>
              <button @click="deletePost(post.id)" class="delete-btn">删除</button>
            </div>
          </div>
          <div class="post-content">{{ post.content }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty">暂无文章，发布第一篇文章吧！</div>
    </div>
  </div>
</template>

<style scoped>
.app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
}

/* 认证相关样式 */
.auth-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.auth-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.tab-btn {
  flex: 1;
  padding: 12px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-btn:hover {
  background-color: #f0f0f0;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.auth-form h2 {
  text-align: center;
  margin: 0 0 20px 0;
  color: #333;
}

.auth-form input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.auth-form button {
  padding: 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.auth-form button:hover:not(:disabled) {
  background-color: #0056b3;
}

.auth-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 用户头部 */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  margin-bottom: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.welcome {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.logout-btn {
  padding: 8px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.logout-btn:hover {
  background-color: #5a6268;
}

.add-post {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.title-input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
}

.content-input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.add-post button {
  padding: 12px 24px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  align-self: flex-start;
}

.add-post button:hover:not(:disabled) {
  background-color: #218838;
}

.add-post button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading,
.empty {
  text-align: center;
  color: #666;
  padding: 40px;
  font-size: 18px;
}

.posts {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.post-item {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.post-title {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  flex: 1;
  margin-right: 15px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.post-date {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
}

.post-content {
  color: #555;
  line-height: 1.6;
  font-size: 16px;
  white-space: pre-wrap;
}

.delete-btn {
  padding: 6px 12px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.delete-btn:hover {
  background-color: #c82333;
}
</style>
