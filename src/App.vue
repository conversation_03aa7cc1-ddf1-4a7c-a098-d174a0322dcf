<script setup>
import { ref, onMounted } from 'vue'
import pb from './pocketbase'

// 响应式数据
const todos = ref([])
const newTodo = ref('')
const loading = ref(false)
const error = ref('')

// 获取所有待办事项
async function getTodos() {
  try {
    loading.value = true
    error.value = ''
    const records = await pb.collection('todos').getFullList()
    todos.value = records
  } catch (err) {
    error.value = '获取数据失败: ' + err.message
    console.error('获取待办事项失败:', err)
  } finally {
    loading.value = false
  }
}

// 添加新的待办事项
async function addTodo() {
  if (!newTodo.value.trim()) return

  try {
    loading.value = true
    error.value = ''
    const data = {
      title: newTodo.value,
      completed: false,
      created: new Date().toISOString(),
    }

    const record = await pb.collection('todos').create(data)
    todos.value.push(record)
    newTodo.value = ''
  } catch (err) {
    error.value = '添加失败: ' + err.message
    console.error('添加待办事项失败:', err)
  } finally {
    loading.value = false
  }
}

// 切换完成状态
async function toggleTodo(todo) {
  try {
    const updatedRecord = await pb.collection('todos').update(todo.id, {
      completed: !todo.completed,
    })

    const index = todos.value.findIndex((t) => t.id === todo.id)
    if (index !== -1) {
      todos.value[index] = updatedRecord
    }
  } catch (err) {
    error.value = '更新失败: ' + err.message
    console.error('更新待办事项失败:', err)
  }
}

// 删除待办事项
async function deleteTodo(todoId) {
  try {
    await pb.collection('todos').delete(todoId)
    todos.value = todos.value.filter((t) => t.id !== todoId)
  } catch (err) {
    error.value = '删除失败: ' + err.message
    console.error('删除待办事项失败:', err)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getTodos()
})
</script>

<template>
  <div class="app">
    <h1>PocketBase 待办事项</h1>

    <!-- 错误提示 -->
    <div v-if="error" class="error">
      {{ error }}
    </div>

    <!-- 添加新待办事项 -->
    <div class="add-todo">
      <input
        v-model="newTodo"
        @keyup.enter="addTodo"
        placeholder="输入新的待办事项..."
        :disabled="loading"
      />
      <button @click="addTodo" :disabled="loading || !newTodo.trim()">
        {{ loading ? '添加中...' : '添加' }}
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && todos.length === 0" class="loading">加载中...</div>

    <!-- 待办事项列表 -->
    <div v-else-if="todos.length > 0" class="todos">
      <div
        v-for="todo in todos"
        :key="todo.id"
        class="todo-item"
        :class="{ completed: todo.completed }"
      >
        <input type="checkbox" :checked="todo.completed" @change="toggleTodo(todo)" />
        <span class="todo-title">{{ todo.title }}</span>
        <span class="todo-date">{{ new Date(todo.created).toLocaleDateString() }}</span>
        <button @click="deleteTodo(todo.id)" class="delete-btn">删除</button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty">暂无待办事项，添加一个试试吧！</div>
  </div>
</template>

<style scoped>
.app {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
}

.add-todo {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.add-todo input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.add-todo button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.add-todo button:hover:not(:disabled) {
  background-color: #0056b3;
}

.add-todo button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading,
.empty {
  text-align: center;
  color: #666;
  padding: 40px;
  font-size: 18px;
}

.todos {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-item.completed .todo-title {
  text-decoration: line-through;
}

.todo-title {
  flex: 1;
  font-size: 16px;
}

.todo-date {
  color: #666;
  font-size: 14px;
}

.delete-btn {
  padding: 5px 10px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.delete-btn:hover {
  background-color: #c82333;
}
</style>
